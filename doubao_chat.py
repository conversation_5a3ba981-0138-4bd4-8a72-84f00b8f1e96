#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import uuid

class DoubaoChat:
    def __init__(self):
        self.base_url = "https://www.doubao.com"
        self.session = requests.Session()
        
        # 从抓包信息中提取的Cookie（去除msToken和a_bogus相关部分）
        self.cookies = {
            'i18next': 'zh',
            'gd_random': 'eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MTQ5MzU5NjkzNjgwNzQxfQ==.yuE0xZkkRp6rqlY66YCqWrFlfN//9LZZZ7S0sT8jJho=',
            'flow_user_country': 'CN',
            'ttcid': '9998073e514b46379ccfd15657ffa06c33',
            's_v_web_id': 'verify_mdqh7kzd_ZkQmYYgh_AW2y_4mCs_95OU_aDZp9boGLZ1u',
            'passport_csrf_token': '23cc9202dee0bd3af72b1d67305320a1',
            'passport_csrf_token_default': '23cc9202dee0bd3af72b1d67305320a1',
            'hook_slardar_session_id': '2025073105274644B91E1243B31013D640,ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910866%7Cd0f892d1e4c839a868e20cdb539884dac45992241cb359c30ef040897bbe4427',
            'passport_mfa_token': 'CjGPhXlg8y7V4IKl5gnYMvo0%2BqNhIn32FEYWNz6gkjjYI4X8hnKQYV7Unk%2BU0WC%2FzeuwGkoKPAAAAAAAAAAAAABPS%2FOfnAf%2F4h5UzB7KpogdYZuez0H1rnw99uy9NGTKi1MfFnCWCMxSVLE%2B%2FXAZYG41LRChlfgNGPax0WwgAiIBAyxRQuI%3D',
            'd_ticket': '3ba103731f1417bb3d92f65489c2cf384b9ef',
            'odin_tt': 'ceee7eb54d94684dae2543622104f0c4c54b16bf7bf53e2998c4381989c54b7a65d33009560a1c9211cc7ee74550542d19171565610c7c33ca884b16e4c1d1b0',
            'n_mh': '3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI',
            'passport_auth_status': '1e0467e7ce1de60752facaf3e7239ed9%2C',
            'passport_auth_status_ss': '1e0467e7ce1de60752facaf3e7239ed9%2C',
            'sid_guard': '53c1e5576dbeb67c1f781749fa771e22%7C1753910897%7C5184000%7CSun%2C+28-Sep-2025+21%3A28%3A17+GMT',
            'uid_tt': 'b615a4154b708bbf57f415bfbf358f8e',
            'uid_tt_ss': 'b615a4154b708bbf57f415bfbf358f8e',
            'sid_tt': '53c1e5576dbeb67c1f781749fa771e22',
            'sessionid': '53c1e5576dbeb67c1f781749fa771e22',
            'sessionid_ss': '53c1e5576dbeb67c1f781749fa771e22',
            'session_tlb_tag': 'sttt%7C6%7CU8HlV22-tnwfeBdJ-nceIv_________SzM3yFwLkszo23AKHohjjcpT0MVXz8bPDuNRWJVotL34%3D',
            'is_staff_user': 'false',
            'sid_ucp_v1': '1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg',
            'ssid_ucp_v1': '1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg',
            'flow_ssr_sidebar_expand': '1',
            'ttwid': '1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910898%7C7dcd3259bc813de5eb6fcaed0dca1e996cdc228ccac1c0065d0c83eb7d1b697d',
            'passport_fe_beating_status': 'true',
            'tt_scid': 'xOJmWMxEZGsbdioWazr17pFNwm8OrYTYLJ2dO01TVpy4KeOvmYDHGWCfSKg1TdFl4dbf'
        }
        
        # 从抓包信息中提取的请求头
        self.headers = {
            'Host': 'www.doubao.com',
            'Connection': 'keep-alive',
            'x-flow-trace': '04-001a82f5f9009ed2001263770d2e08fa-000e768cf5e2e36b-01',
            'sec-ch-ua-platform': '"Android"',
            'sec-ch-ua': '"Chromium";v="130", "Android WebView";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile': '?1',
            'Agw-Js-Conv': 'str, str',
            'last-event-id': 'undefined',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 15; PJD110 Build/AP3A.240617.008) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36',
            'content-type': 'application/json',
            'Accept': '*/*',
            'Origin': 'https://www.doubao.com',
            'X-Requested-With': 'mark.via',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://www.doubao.com/chat/local_1191721692321224',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
        }
        
        # 设置session的cookies和headers
        self.session.cookies.update(self.cookies)
        self.session.headers.update(self.headers)
    
    def generate_local_message_id(self):
        """生成本地消息ID"""
        return str(uuid.uuid4())
    
    def generate_local_conversation_id(self):
        """生成本地对话ID"""
        timestamp = int(time.time() * 1000)
        return f"local_{timestamp}"
    
    def send_message(self, message_text):
        """发送消息到豆包"""
        # 生成本地ID
        local_message_id = self.generate_local_message_id()
        local_conversation_id = self.generate_local_conversation_id()
        
        # 构建请求URL（从抓包信息中提取的参数）
        url = f"{self.base_url}/samantha/chat/completion"
        params = {
            'aid': '497858',
            'device_id': '7532989318484657699',
            'device_platform': 'web',
            'language': 'zh',
            'pc_version': '2.29.3',
            'pkg_type': 'release_version',
            'real_aid': '497858',
            'region': 'CN',
            'samantha_web': '1',
            'sys_region': 'CN',
            'tea_uuid': '7532989324985157172',
            'use-olympus-account': '1',
            'version_code': '20800',
            'web_id': '7532989324985157172'
        }
        
        # 构建请求体（基于抓包信息）
        payload = {
            "messages": [
                {
                    "content": json.dumps({"text": message_text}),
                    "content_type": 2001,
                    "attachments": [],
                    "references": []
                }
            ],
            "completion_option": {
                "is_regen": False,
                "with_suggest": True,
                "need_create_conversation": True,
                "launch_stage": 1,
                "is_replace": False,
                "is_delete": False,
                "message_from": 0,
                "use_deep_think": False,
                "use_auto_cot": True,
                "resend_for_regen": False,
                "event_id": "0"
            },
            "evaluate_option": {
                "web_ab_params": ""
            },
            "conversation_id": "0",
            "local_conversation_id": local_conversation_id,
            "local_message_id": local_message_id
        }
        
        try:
            # 发送请求
            response = self.session.post(url, params=params, json=payload, stream=True)
            response.raise_for_status()
            
            # 解析流式响应
            return self.parse_stream_response(response)
            
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            return None
    
    def parse_stream_response(self, response):
        """解析流式响应"""
        full_text = ""
        suggestions = []

        print("调试信息 - 响应状态码:", response.status_code)
        print("调试信息 - 响应头:", dict(response.headers))

        try:
            for line in response.iter_lines(decode_unicode=True):
                if not line:
                    continue

                print(f"调试信息 - 原始行: {repr(line)}")

                if line.startswith('data: '):
                    data_str = line[6:]  # 去掉 'data: ' 前缀

                    if data_str.strip() == '' or data_str.strip() == '{}':
                        continue

                    try:
                        data = json.loads(data_str)
                        print(f"调试信息 - 解析的数据: {data}")

                        event_data_str = data.get('event_data', '{}')
                        event_data = json.loads(event_data_str)
                        print(f"调试信息 - 事件数据: {event_data}")

                        # 处理消息内容
                        if 'message' in event_data:
                            message = event_data['message']
                            content_type = message.get('content_type')

                            if content_type == 2001:  # 文本消息
                                content_str = message.get('content', '{}')
                                content = json.loads(content_str)
                                text = content.get('text', '')
                                if text:
                                    full_text += text
                                    print(text, end='', flush=True)

                            elif content_type == 2002:  # 建议消息
                                content_str = message.get('content', '{}')
                                content = json.loads(content_str)
                                suggest = content.get('suggest', '')
                                if suggest:
                                    suggestions.append(suggest)

                    except json.JSONDecodeError as e:
                        print(f"调试信息 - JSON解析错误: {e}, 数据: {repr(data_str)}")
                        continue

        except Exception as e:
            print(f"\n解析响应时出错: {e}")
            import traceback
            traceback.print_exc()

        print()  # 换行

        if suggestions:
            print("\n建议的问题:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"{i}. {suggestion}")

        return {
            'text': full_text,
            'suggestions': suggestions
        }

def main():
    """主函数"""
    chat = DoubaoChat()
    
    print("豆包聊天机器人")
    print("输入 'quit' 或 'exit' 退出")
    print("-" * 50)
    
    while True:
        try:
            user_input = input("\n你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见!")
                break
            
            if not user_input:
                continue
            
            print("\n豆包: ", end='')
            result = chat.send_message(user_input)
            
            if result is None:
                print("发送消息失败，请重试。")
                
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"\n发生错误: {e}")

if __name__ == "__main__":
    main()
